import { PlainMessage } from '@bufbuild/protobuf';
import { createQueryKeyStore } from '@lukemorales/query-key-factory';

import { GetInstanceClusterCommandRequest } from './argocd/v1/argocd_pb';
import { GroupByInterval, PromotionFilter } from './generated';
import { GetInstanceAgentCommandRequest, KargoAgentFilter } from './kargo/v1/kargo_pb';
import { AuditLogArchiveFilters, ListCustomRolesRequest } from './organization/v1/organization_pb';

export const queryKeys = createQueryKeyStore({
  auth: {
    getOIDCProvider: (discoveryUrl: string) => ({
      queryKey: [discoveryUrl]
    })
  },
  user: {
    notifications: (offset: number, limit: number) => ({
      queryKey: ['notificationsList', String(offset), String(limit)]
    }),
    shortlistNotifications: () => ({
      queryKey: ['shortlistNotifications']
    }),
    notificationsSettings: () => ({
      queryKey: ['notificationsSettings']
    })
  },
  users: {
    item: () => ({
      queryKey: ['user']
    })
  },
  argocd: {
    list: (organizationId: string, workspaceId?: string) => ({
      queryKey: [organizationId, workspaceId]
    }),
    item: (organizationId: string, name: string) => ({
      queryKey: [organizationId, name]
    }),
    clustersList: (organizationId: string, instanceId: string, workspaceId?: string) => ({
      queryKey: [organizationId, instanceId, workspaceId]
    }),
    aiAssistantUsage: (organizationId: string, instances: string[] = []) => ({
      queryKey: [organizationId, ...instances]
    }),
    getClusterCommand: ({
      id,
      instanceId,
      locationOrigin,
      offline,
      organizationId,
      type,
      skipNamespace,
      commandFor
    }: PlainMessage<GetInstanceClusterCommandRequest>) => ({
      queryKey: [
        id,
        instanceId,
        locationOrigin,
        String(offline),
        organizationId,
        type,
        skipNamespace,
        commandFor
      ]
    }),
    accounts: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    getNotificationSettings: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    getNotificationCatalog: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    getCSS: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    resourceCustomization: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    configurationSummary: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    configManagementPlugins: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    appsetSecret: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    imageUpdaterSettings: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    outdatedClusters: (
      organizationId: string,
      instanceId: string,
      excludeAgentVersion: string,
      limit: number,
      nameLike: string
    ) => ({
      queryKey: [organizationId, instanceId, excludeAgentVersion, limit, nameLike]
    }),
    addonReposList: (organizationId: string, workspaceId: string, instanceId: string) => ({
      queryKey: [organizationId, workspaceId, instanceId]
    }),
    addonRepoDetails: (
      organizationId: string,
      workspaceId: string,
      instanceId: string,
      id: string
    ) => ({
      queryKey: [organizationId, workspaceId, instanceId, id]
    }),
    reposList: (organizationId: string, workspaceId: string, instanceId: string) => ({
      queryKey: [organizationId, workspaceId, instanceId]
    }),
    addonsList: (
      organizationId: string,
      workspaceId: string,
      instanceId: string,
      filters: string
    ) => ({
      queryKey: [organizationId, workspaceId, instanceId, filters]
    }),
    addonDetails: (organizationId: string, id: string) => ({
      queryKey: [organizationId, id]
    }),
    addonErrors: (organizationId: string, workspaceId: string, instanceId: string, id: string) => ({
      queryKey: [organizationId, workspaceId, instanceId, id]
    }),
    artifactHubAddons: (filters: string) => ({
      queryKey: [filters]
    }),
    artifactHubAddonDetails: (repoName: string, packageName: string) => ({
      queryKey: [repoName, packageName]
    }),
    listInstalledMarketplaceAddons: (
      organizationId: string,
      workspaceId: string,
      instanceId: string,
      filters?: string
    ) => ({
      queryKey: [organizationId, workspaceId, instanceId, filters]
    })
  },
  organizations: {
    item: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    mfaStatus: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    apiKeys: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    members: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    invitees: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    auditLogs: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    auditLogArchives: (organizationId: string, filters: AuditLogArchiveFilters) => ({
      queryKey: [organizationId, filters]
    }),
    userRole: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    customerDetails: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    ssoConfiguration: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    featureStatuses: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    oidcMap: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    oidcTeamMap: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    billingAddons: (organizationId: string, plan: string) => ({
      queryKey: [organizationId, plan]
    }),
    plans: () => ({
      queryKey: ['plans']
    }),
    customRoles: (
      organizationId: string,
      filter?: Omit<PlainMessage<ListCustomRolesRequest>, 'organizationId'>
    ) => ({
      queryKey: [organizationId, filter]
    }),
    teams: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    teamMembers: (organizationId: string, teamName: string) => ({
      queryKey: [organizationId, teamName]
    }),
    notificationConfigs: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    notificationConfig: (organizationId: string, id: string) => ({
      queryKey: [organizationId, id]
    }),
    notificationDeliveryHistory: (
      organizationId: string,
      id: string,
      limit: bigint,
      offset: bigint
    ) => ({
      queryKey: [organizationId, id, String(limit), String(offset)]
    }),
    notificationDeliveryHistoryDetails: (organizationId: string, configId: string, id: string) => ({
      queryKey: [organizationId, configId, id]
    }),
    domain: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    permissions: (organizationId: string) => ({
      queryKey: [organizationId]
    })
  },
  kargo: {
    list: (organizationId: string, workspaceId?: string) => ({
      queryKey: [organizationId, workspaceId]
    }),
    item: (organizationId: string, name: string) => ({
      queryKey: [organizationId, name]
    }),
    agentsList: (
      organizationId: string,
      instanceId: string,
      workspaceId?: string,
      filter?: PlainMessage<KargoAgentFilter>
    ) => ({
      queryKey: [organizationId, workspaceId, instanceId, filter]
    }),
    clusterCommand: ({
      id,
      instanceId,
      locationOrigin,
      organizationId,
      type,
      skipNamespace
    }: PlainMessage<GetInstanceAgentCommandRequest>) => ({
      queryKey: [id, instanceId, locationOrigin, organizationId, type, skipNamespace]
    }),
    promotionStats: (
      organizationId: string,
      filter: PromotionFilter,
      timerange: [Date?, Date?],
      interval: GroupByInterval
    ) => ({
      queryKey: [organizationId, filter, 'promotionStats', timerange, interval]
    }),
    promotionEvents: (
      organizationId: string,
      filter: PromotionFilter,
      timerange: [Date?, Date?],
      interval: GroupByInterval
    ) => ({
      queryKey: [organizationId, filter, 'promotionEvents', timerange, interval]
    })
  },
  system: {
    settings: () => ({
      queryKey: ['settings']
    }),
    argoCDAgentSpec: () => ({
      queryKey: ['spec']
    }),
    kargoAgentSpec: () => ({
      queryKey: ['spec']
    }),
    argoCDVersions: () => ({
      queryKey: ['argoCDVersions']
    }),
    kargoVersions: () => ({
      queryKey: ['argoCDVersions']
    }),
    argoCDExtensions: () => ({
      queryKey: ['argoCDExtensions']
    }),
    stableCLIVersion: () => ({
      queryKey: ['stableCLIVersion']
    }),
    selfhostedCompatibleCLIVersion: () => ({
      queryKey: ['selfhostedCompatibleCLIVersion']
    }),
    latestAgentVersion: () => ({
      queryKey: ['latestAgentVersion']
    }),
    agentVersions: () => ({
      queryKey: ['agentVersions']
    }),
    currentLicense: () => ({
      queryKey: ['currentLicense']
    }),
    defaultLicense: () => ({
      queryKey: ['defaultLicense']
    }),
    licenseUsage: () => ({
      queryKey: ['licenseUsage']
    }),
    announcement: () => ({
      queryKey: ['announcement']
    }),
    webhookEvents: () => ({
      queryKey: ['webhookEvents']
    })
  },
  workspace: {
    list: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    item: (organizationId: string, id: string) => ({
      queryKey: [organizationId, id]
    }),
    members: (organizationId: string, id: string) => ({
      queryKey: [organizationId, id]
    }),
    apiKeys: (organizationId: string, id: string) => ({
      queryKey: [organizationId, id]
    }),
    customRoles: (organizationId: string, id: string) => ({
      queryKey: [organizationId, id]
    })
  },
  kubevision: {
    list: (organizationId: string, type: string, params: string) => ({
      queryKey: [organizationId, type, params]
    }),
    types: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    clusters: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    namespaces: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    deprecatedAPIs: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    timelineEvents: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    timelineResources: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    resourceDetail: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    container: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    manifest: (organizationId: string, filters: string) => ({
      queryKey: [organizationId, filters]
    }),
    events: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    auditLogs: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    podLogs: (organizationId: string, resourceId: string, params: string) => ({
      queryKey: [organizationId, resourceId, params]
    }),
    assistantSuggestion: (organizationId: string, resourceId: string, reqId: number) => ({
      queryKey: [organizationId, resourceId, reqId.toString()]
    }),
    nodes: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    pods: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    namespacesDetails: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    namespaceDetail: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    nodeDetail: (organizationId: string, nodeId: string, params: string) => ({
      queryKey: [organizationId, nodeId, params]
    }),
    podDetail: (organizationId: string, podId: string, params: string) => ({
      queryKey: [organizationId, podId, params]
    }),
    clusterDetail: (organizationId: string, instanceId: string, clusterId: string) => ({
      queryKey: [organizationId, instanceId, clusterId]
    }),
    image: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    kubevisionUsage: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    }),
    summary: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    instances: (organizationId: string) => ({
      queryKey: [organizationId]
    }),
    instance: (organizationId: string, instanceId: string) => ({
      queryKey: [organizationId, instanceId]
    }),
    incidents: (organizationId: string, instanceId: string, params: string) => ({
      queryKey: [organizationId, instanceId, params]
    }),
    incident: (organizationId: string, id: string) => ({
      queryKey: [organizationId, id]
    })
  },
  ai: {
    conversations: (organizationId: string, instanceId: string, params: string) => ({
      queryKey: [organizationId, instanceId, params]
    }),
    getConversation: (organizationId: string, instanceId: string, id: string) => ({
      queryKey: [organizationId, instanceId, id]
    }),
    watchConversation: (organizationId: string, instanceId: string, id: string) => ({
      queryKey: [organizationId, instanceId, id]
    }),
    suggestions: (organizationId: string, conversationId: string, params: string) => ({
      queryKey: [organizationId, conversationId, params]
    }),
    newConversationContexts: (organizationId: string, instanceId: string, id: string) => ({
      queryKey: [organizationId, instanceId, id]
    })
  },
  spotlight: {
    search: (organizationId: string, params: string) => ({
      queryKey: [organizationId, params]
    })
  }
});
