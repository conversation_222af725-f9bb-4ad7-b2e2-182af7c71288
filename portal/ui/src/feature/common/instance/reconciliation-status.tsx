import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Tooltip } from 'antd';

import { IconPropsForReconciliation } from '@ui/instances/components';
import { Instance } from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import { KargoInstance } from '@ui/lib/apiclient/kargo/v1/kargo_pb';
import { StatusCode } from '@ui/lib/apiclient/types/status/reconciliation/v1/reconciliation_pb';

export const ReconciliationStatus = ({ instance }: { instance: Instance | KargoInstance }) => {
  if (instance.deleteTime || instance.reconciliationStatus?.code === StatusCode.SUCCESSFUL) {
    return null;
  }

  return (
    <Tooltip title={instance?.reconciliationStatus.message}>
      <FontAwesomeIcon {...IconPropsForReconciliation(instance?.reconciliationStatus.code)} />
    </Tooltip>
  );
};
