package argocd

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	grpcgatewayruntime "github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/api/akuity/v1alpha1"
	argocdv1alpha1 "github.com/akuityio/akuity-platform/api/argocd/v1alpha1"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	idv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/id/v1"
)

func (s *ArgoCDV1Server) ExportInstance(
	ctx context.Context,
	req *argocdv1.ExportInstanceRequest,
) (*argocdv1.ExportInstanceResponse, error) {
	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	var err error
	var instance *instances.ArgoCDInstance

	if req.IdType == idv1.Type_NAME {
		instance, err = instanceSvc.GetInstanceByName(ctx, req.Id)
	} else {
		instance, err = instanceSvc.GetInstanceByID(ctx, req.Id)
	}
	if err != nil {
		return nil, err
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(instance.WorkspaceID.String, instance.ID)); err != nil {
		return nil, err
	}

	instanceV1, err := argocdutil.NewArgoCDInstanceV1(*instance, s.cfg.InstanceProgressingDeadline, argocdutil.IsComponentVersionSupported(instance.Version.String, s.versions) != nil)
	if err != nil {
		return nil, err
	}

	var marshaller grpcgatewayruntime.JSONPb
	objBytes, err := marshaller.Marshal(instanceV1.Spec)
	if err != nil {
		return nil, err
	}
	var instanceSpec v1alpha1.InstanceSpec
	if err := json.Unmarshal(objBytes, &instanceSpec); err != nil {
		return nil, err
	}

	argocd := v1alpha1.ArgoCD{
		TypeMeta:   metav1.TypeMeta{Kind: "ArgoCD", APIVersion: "argocd.akuity.io/v1alpha1"},
		ObjectMeta: metav1.ObjectMeta{Name: instance.Name},
		Spec: v1alpha1.ArgoCDSpec{
			Description:  instance.Description.String,
			Version:      instance.Version.String,
			InstanceSpec: instanceSpec,
			Shard:        instance.Shard,
		},
	}

	res := &argocdv1.ExportInstanceResponse{}
	argocdMap := map[string]interface{}{}
	if err := types.RemarshalTo(argocd, &argocdMap); err != nil {
		return nil, err
	}
	if res.Argocd, err = structpb.NewStruct(argocdMap); err != nil {
		return nil, err
	}

	existingClusters, err := instanceSvc.GetInstanceClusters(ctx, instance.InstanceID)
	if err != nil {
		return nil, err
	}
	for _, c := range existingClusters {
		cluster, err := newKubeCluster(c)
		if err != nil {
			return nil, err
		}
		clusterMap := map[string]interface{}{}
		if err := types.RemarshalTo(cluster, &clusterMap); err != nil {
			return nil, err
		}
		clusterStruct, err := structpb.NewStruct(clusterMap)
		if err != nil {
			return nil, err
		}
		res.Clusters = append(res.Clusters, clusterStruct)
	}

	existingCMPs, err := s.GetInstanceConfigManagementPlugins(ctx, &argocdv1.GetInstanceConfigManagementPluginsRequest{
		OrganizationId: req.OrganizationId,
		Id:             instance.InstanceID,
	})
	if err != nil {
		return nil, err
	}
	for _, c := range existingCMPs.Plugins {
		cmp, err := newKubeCMP(c)
		if err != nil {
			return nil, err
		}
		cmpMap := map[string]interface{}{}
		if err := types.RemarshalTo(cmp, &cmpMap); err != nil {
			return nil, err
		}
		cmpStruct, err := structpb.NewStruct(cmpMap)
		if err != nil {
			return nil, err
		}
		res.ConfigManagementPlugins = append(res.ConfigManagementPlugins, cmpStruct)
	}

	if err := instance.ArgocdCM.Unmarshal(&res.ArgocdConfigmap); err != nil {
		return nil, err
	}
	if err := instance.ArgocdNotificationsCM.Unmarshal(&res.NotificationsConfigmap); err != nil {
		return nil, err
	}
	if err := instance.ArgocdRbacCM.Unmarshal(&res.ArgocdRbacConfigmap); err != nil {
		return nil, err
	}
	if err := instance.ArgocdImageUpdaterCM.Unmarshal(&res.ImageUpdaterConfigmap); err != nil {
		return nil, err
	}
	if err := instance.ArgocdImageUpdaterSSHCM.Unmarshal(&res.ImageUpdaterSshConfigmap); err != nil {
		return nil, err
	}
	knownHostsCM, tlsCertsCM, apps, appSets, appProjects, err := s.getK3sResources(ctx, instance)
	if err != nil {
		return nil, err
	}
	res.ArgocdKnownHostsConfigmap, err = mapToStruct(knownHostsCM.Data)
	if err != nil {
		return nil, err
	}
	res.ArgocdTlsCertsConfigmap, err = mapToStruct(tlsCertsCM.Data)
	if err != nil {
		return nil, err
	}
	for _, a := range apps.Items {
		// skip the Application which is managed by other Applications or ApplicationSets
		if isManagedObject(a) {
			continue
		}
		o, err := structpb.NewStruct(a.Object)
		if err != nil {
			return nil, err
		}
		res.Applications = append(res.Applications, o)
	}
	for _, a := range appSets.Items {
		// skip the ApplicationSet which is managed by other Applications or ApplicationSets
		if isManagedObject(a) {
			continue
		}
		o, err := structpb.NewStruct(a.Object)
		if err != nil {
			return nil, err
		}
		res.ApplicationSets = append(res.ApplicationSets, o)
	}
	for _, a := range appProjects.Items {
		// skip the AppProject which is managed by other Applications or ApplicationSets
		if isManagedObject(a) {
			continue
		}
		o, err := structpb.NewStruct(a.Object)
		if err != nil {
			return nil, err
		}
		res.AppProjects = append(res.AppProjects, o)
	}
	return res, nil
}

func (s *ArgoCDV1Server) getK3sResources(ctx context.Context, instance *instances.ArgoCDInstance) (knownHostsCM, tlsCertsCM *corev1.ConfigMap, applications, applicationSets, appProjects *unstructured.UnstructuredList, err error) {
	timeout := time.After(120 * time.Second)
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-timeout:
			return nil, nil, nil, nil, nil, errors.New("timeout while getting manifests")
		case <-ctx.Done():
			s.log.WithValues("ctx.Err", ctx.Err()).Info("context canceled while getting manifests")
			return nil, nil, nil, nil, nil, context.Canceled
		case <-ticker.C:

			var k3sKubeClient kubernetes.Interface
			var k3sDynamicClient dynamic.Interface

			if instance.Shard == "" {
				tnt, err := client.NewArgoCDTenant(s.hostRestConfig, *s.log, instance.InstanceID)
				if err != nil {
					return nil, nil, nil, nil, nil, err
				}
				k3sKubeClient, err = tnt.ControlPlaneKubeClientset(ctx)
				if err != nil {
					// If the k3s kubeconfig doesn't exist, we skip the next steps and wait until the kubeconfig secret is created.
					if k8serrors.IsNotFound(err) {
						continue
					}
					// We don't return the error directly since it may contain AKP implementation details.
					return nil, nil, nil, nil, nil, status.Errorf(codes.InvalidArgument, "The instance is still being provisioned. Please try again in a few minutes")
				}
				k3sDynamicClient, err = tnt.ControlPlaneDynamicClientset(ctx)
				if err != nil {
					// If the k3s kubeconfig doesn't exist, we skip the next steps and wait until the kubeconfig secret is created.
					if k8serrors.IsNotFound(err) {
						continue
					}
					// We don't return the error directly since it may contain AKP implementation details.
					return nil, nil, nil, nil, nil, status.Errorf(codes.InvalidArgument, "The instance is still being provisioned. Please try again in a few minutes")
				}
			} else {
				privateSpec, err := instance.GetPrivateSpec()
				if err != nil {
					return nil, nil, nil, nil, nil, err
				}
				if privateSpec.Kubeconfig == nil {
					// If the k3s kubeconfig doesn't exist, we skip the next steps and wait until the kubeconfig secret is created.
					continue
				}

				restConfig, err := privateSpec.GetRestConfig()
				if err != nil {
					return nil, nil, nil, nil, nil, err
				}
				k3sKubeClient, err = kubernetes.NewForConfig(restConfig)
				if err != nil {
					return nil, nil, nil, nil, nil, err
				}
				k3sDynamicClient, err = dynamic.NewForConfig(restConfig)
				if err != nil {
					return nil, nil, nil, nil, nil, err
				}
			}
			// Get argocd-ssh-known-host-cm and argocd-tls-certs-cm Configmaps.
			knownHostsCM, err = k3sKubeClient.CoreV1().ConfigMaps(argoproj.K3sArgoCDNamespace).Get(ctx, argoproj.ArgoCDKnownHostsConfigMapName, metav1.GetOptions{})
			if err != nil {
				continue
			}
			tlsCertsCM, err = k3sKubeClient.CoreV1().ConfigMaps(argoproj.K3sArgoCDNamespace).Get(ctx, argoproj.ArgoCDTLSCertsConfigMapName, metav1.GetOptions{})
			if err != nil {
				continue
			}

			// Get Applications, ApplicationSets and AppProjects.
			applications, err = k3sDynamicClient.Resource(argocdutil.ApplicationGVR).Namespace(argoproj.K3sArgoCDNamespace).List(ctx, metav1.ListOptions{})
			if err != nil {
				continue
			}
			applicationSets, err = k3sDynamicClient.Resource(argocdutil.ApplicationsetGVR).Namespace(argoproj.K3sArgoCDNamespace).List(ctx, metav1.ListOptions{})
			if err != nil {
				continue
			}
			appProjects, err = k3sDynamicClient.Resource(argocdutil.AppprojectsGVR).Namespace(argoproj.K3sArgoCDNamespace).List(ctx, metav1.ListOptions{})
			if err != nil {
				continue
			}

			// Remove unmanaged fields from each resource before performing diff.
			shared.RemoveUnmanagedFields(applications)
			shared.RemoveUnmanagedFields(applicationSets)
			shared.RemoveUnmanagedFields(appProjects)

			return
		}
	}
}

func mapToStruct(data map[string]string) (*structpb.Struct, error) {
	m := make(map[string]interface{})
	for k, v := range data {
		m[k] = v
	}
	return structpb.NewStruct(m)
}

func newKubeCMP(cmp *argocdv1.ConfigManagementPlugin) (*argocdv1alpha1.ConfigManagementPlugin, error) {
	if cmp == nil {
		return nil, nil
	}
	spec := argocdv1alpha1.PluginSpec{}
	if cmp.Spec != nil {
		specData, err := json.Marshal(cmp.Spec)
		if err != nil {
			return nil, err
		}
		if err := json.Unmarshal(specData, &spec); err != nil {
			return nil, err
		}
	}

	return &argocdv1alpha1.ConfigManagementPlugin{
		TypeMeta: metav1.TypeMeta{Kind: "ConfigManagementPlugin", APIVersion: "argoproj.io/v1alpha1"},
		ObjectMeta: metav1.ObjectMeta{
			Name: cmp.Name,
			Annotations: map[string]string{
				argocdv1alpha1.AnnotationCMPImage:   cmp.Image,
				argocdv1alpha1.AnnotationCMPEnabled: strconv.FormatBool(cmp.Enabled),
			},
		},
		Spec: spec,
	}, nil
}

func newKubeCluster(cluster *models.ArgoCDCluster) (*v1alpha1.Cluster, error) {
	if cluster == nil {
		return nil, nil
	}
	spec, err := cluster.GetSpec()
	if err != nil {
		return nil, err
	}
	var kustomization runtime.RawExtension
	if spec.Kustomization != nil {
		raw, err := json.Marshal(spec.Kustomization.AsMap())
		if err != nil {
			return nil, err
		}
		kustomization.Raw = raw
	}
	var managedClusterConfig *v1alpha1.ManagedClusterConfig
	if spec.ManagedClusterConfig != nil {
		managedClusterConfig = &v1alpha1.ManagedClusterConfig{
			SecretName: spec.ManagedClusterConfig.SecretName,
			SecretKey:  spec.ManagedClusterConfig.SecretKey,
		}
	}
	var autoscalerConfig *v1alpha1.AutoScalerConfig
	if spec.AutoscalerConfig != nil {
		autoscalerConfig = &v1alpha1.AutoScalerConfig{}
		if spec.AutoscalerConfig.ApplicationController != nil {
			autoscalerConfig.ApplicationController = &v1alpha1.AppControllerAutoScalingConfig{
				ResourceMinimum: &v1alpha1.Resources{
					Mem: spec.AutoscalerConfig.ApplicationController.ResourceMinimum.Memory.String(),
					Cpu: spec.AutoscalerConfig.ApplicationController.ResourceMinimum.CPU.String(),
				},
				ResourceMaximum: &v1alpha1.Resources{
					Mem: spec.AutoscalerConfig.ApplicationController.ResourceMaximum.Memory.String(),
					Cpu: spec.AutoscalerConfig.ApplicationController.ResourceMaximum.CPU.String(),
				},
			}
		}
		if spec.AutoscalerConfig.RepoServer != nil {
			autoscalerConfig.RepoServer = &v1alpha1.RepoServerAutoScalingConfig{
				ResourceMinimum: &v1alpha1.Resources{
					Mem: spec.AutoscalerConfig.RepoServer.ResourceMinimum.Memory.String(),
					Cpu: spec.AutoscalerConfig.RepoServer.ResourceMinimum.CPU.String(),
				},
				ResourceMaximum: &v1alpha1.Resources{
					Mem: spec.AutoscalerConfig.RepoServer.ResourceMaximum.Memory.String(),
					Cpu: spec.AutoscalerConfig.RepoServer.ResourceMaximum.CPU.String(),
				},
				ReplicaMaximum: spec.AutoscalerConfig.RepoServer.ReplicaMaximum,
				ReplicaMinimum: spec.AutoscalerConfig.RepoServer.ReplicaMinimum,
			}
		}
	}
	var directClusterSpec *v1alpha1.DirectClusterSpec
	if spec.DirectClusterSpec != nil {
		if spec.DirectClusterSpec.Type.IsKargo() {
			directClusterSpec = &v1alpha1.DirectClusterSpec{
				ClusterType:     "kargo",
				KargoInstanceId: &spec.DirectClusterSpec.KargoInstanceID,
			}
		} else {
			return nil, nil
		}
	}
	return &v1alpha1.Cluster{
		TypeMeta: metav1.TypeMeta{Kind: "Cluster", APIVersion: "argocd.akuity.io/v1alpha1"},
		ObjectMeta: metav1.ObjectMeta{
			Name:        cluster.Name,
			Namespace:   cluster.Namespace,
			Labels:      spec.Labels,
			Annotations: spec.Annotations,
		},
		Spec: v1alpha1.ClusterSpec{
			Description:     cluster.Description.String,
			NamespaceScoped: cluster.NamespaceScoped,
			Data: v1alpha1.ClusterData{
				Size:                            v1alpha1.ClusterSize(spec.Size),
				AutoUpgradeDisabled:             &cluster.AutoUpgradeDisabled,
				Kustomization:                   kustomization,
				AppReplication:                  &spec.AppReplication,
				TargetVersion:                   spec.TargetVersion,
				RedisTunneling:                  &spec.RedisTunneling,
				DatadogAnnotationsEnabled:       &spec.DatadogAnnotationsEnabled,
				EksAddonEnabled:                 &spec.EKSAddonEnabled,
				ManagedClusterConfig:            managedClusterConfig,
				MultiClusterK8SDashboardEnabled: &spec.MultiClusterK8SDashboardEnabled,
				AutoscalerConfig:                autoscalerConfig,
				Project:                         spec.Project,
				DirectClusterSpec:               directClusterSpec,
				Compatibility: &v1alpha1.ClusterCompatibility{
					Ipv6Only: spec.Compatibility.IPV6Only,
				},
				ArgocdNotificationsSettings: &v1alpha1.ClusterArgoCDNotificationsSettings{
					InClusterSettings: spec.ArgoCDNotifications.InClusterSettings,
				},
			},
		},
	}, nil
}
